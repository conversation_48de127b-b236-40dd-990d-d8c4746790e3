<!DOCTYPE html>
<html>
<head>
    <title>User Management (Traditional)</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .message { color: green; margin-bottom: 10px; }
        .error { color: red; margin-bottom: 10px; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        table, th, td { border: 1px solid #ddd; }
        th, td { padding: 10px; text-align: left; }
        th { background-color: #f2f2f2; }
        form { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; background-color: #f9f9f9; }
        label { display: block; margin-bottom: 5px; }
        input[type="text"], input[type="email"] { width: 300px; padding: 5px; margin-bottom: 10px; }
        button { padding: 8px 15px; background-color: #4CAF50; color: white; border: none; cursor: pointer; }
        button.delete { background-color: #f44336; }
        button.edit { background-color: #2196F3; }
        button.cancel { background-color: #607D8B; }
    </style>
</head>
<body>
    <h1>User Management (Traditional with Page Reloads)</h1>

    <div id="message" class="message" style="display: none;"></div>
    <div id="error" class="error" style="display: none;"></div>

    <!-- User Form -->
    <form method="post" action="/add-user">
        <h2 id="form-title">Add New User</h2>
        
        <input type="hidden" id="user-id" name="id" value="">

        <div>
            <label for="name">Name:</label>
            <input type="text" id="name" name="name" value="" required>
        </div>

        <div>
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" value="" required>
        </div>

        <div>
            <button type="submit" name="save">Save User</button>
            <a href="/users-old.html"><button type="button" class="cancel">Cancel</button></a>
        </div>
    </form>

    <!-- Users Table -->
    <h2>Users List</h2>

    <!-- USERS_TABLE_PLACEHOLDER -->
</body>
</html>



